package org.befun.core.generator;

import java.net.Inet4Address;
import java.net.UnknownHostException;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.zip.CRC32;

/**
 * 分布式ID生成器，基于Twitter的Snowflake版本 (定制版本)
 *
 * <AUTHOR>
 */
public class Snowflake {
    /** 起始时间戳 2021年5月1日 GMT */
    private static final Long START_TIME = 1619798400000L;

    /** 机器id所占长度 */
    private static final int WORK_LEN = 3;

    /** 数据中心id所占长度 */
    private static final int DATA_LEN = 3;

    /** 序号所占长度 */
    private static final int SEQUENCE_LEN = 10;

    /** 机器id最大值 31 */
    private static final int WORK_MAX_NUM = ~(-1 << WORK_LEN);

    /** 数据中心id最大值 31 */
    private static final int DATA_MAX_NUM = ~(-1 << DATA_LEN);

    /**
     * 工作机器ID(0~31)
     */
    private long workerId = 0L;

    /**
     * 数据中心ID(0~31)
     */
    private long datacenterId = 0L;

    /**
     * 毫秒内序列(0~4095)
     */
    private long sequence = 0L;

    /**
     * 上次生成ID的时间截
     */
    private long lastTimestamp = -1L;

    /**
     * ctor: default worker and datacenterId
     */
    public Snowflake() {
        this.workerId = getWorkId();
        this.datacenterId = getDataId();
    }

    /**
     * ctor: default worker and datacenterId
     */
    public Snowflake(int workerId, int dataId) {
        this.workerId = workerId;
        this.datacenterId = dataId;
    }

    /**
     * 获取字符串s的字节数组，然后将数组的元素相加，对（max+1）取余
     */
    private static int getHostId(String s, int max){
        CRC32 crc = new CRC32();
        crc.update(s.getBytes(StandardCharsets.UTF_8));

        // Get the hash value and take modulus with maxValue
        long hash = crc.getValue();
        int uniqueId = (int) (hash % (max + 1)); // Ensure it's <= maxValue

        // Ensure the uniqueId is positive
        return Math.abs(uniqueId);
    }

    /**
     * 根据 host address 取余，发生异常就获取 0到31之间的随机数
     */
    public static int getWorkId(){
        try {
            return getHostId(Inet4Address.getLocalHost().getHostAddress(), WORK_MAX_NUM);
        } catch (UnknownHostException e) {
            return new Random().nextInt(WORK_MAX_NUM + 1);
        }
    }

    /**
     * 根据 host name 取余，发生异常就获取 0到31之间的随机数
     */
    public static int getDataId() {
        try {
            return getHostId(Inet4Address.getLocalHost().getHostName(), DATA_MAX_NUM);
        } catch (UnknownHostException e) {
            return new Random().nextInt(DATA_MAX_NUM + 1);
        }
    }

    /**
     * 获得下一个ID (该方法是线程安全的)
     *
     * @return SnowflakeId
     */
    public synchronized long nextId() {
        long timestamp = timeGen();
        // 如果当前时间小于上一次ID生成的时间戳，说明系统时钟回退过这个时候应当抛出异常
        if (timestamp < lastTimestamp) {
            throw new RuntimeException(
                    String.format("Clock moved backwards.  Refusing to generate id for %d milliseconds", lastTimestamp - timestamp));
        }
        // 如果是同一时间生成的，则进行毫秒内序列
        // 序列在id中占的位数
        if (lastTimestamp == timestamp) {
            // 生成序列的掩码，这里为4095 (0b111111111111=0xfff=4095)
            long sequenceMask = ~(-1L << SEQUENCE_LEN);
            sequence = (sequence + 1) & sequenceMask;
            // 毫秒内序列溢出
            if (sequence == 0) {
                // 阻塞到下一个毫秒,获得新的时间戳
                timestamp = tilNextMillis(lastTimestamp);
            }
        }
        // 时间戳改变，毫秒内序列重置
        else {
            sequence = 0L;
        }
        System.out.println("sequence: " + sequence);

        // 上次生成ID的时间截
        lastTimestamp = timestamp;
        long datacenterIdShift = SEQUENCE_LEN + WORK_LEN;
        // 时间截向左移22位(5+5+12)
        long timestampLeftShift = SEQUENCE_LEN + WORK_LEN + DATA_LEN;
        return ((timestamp - START_TIME) << timestampLeftShift)
                | (datacenterId << datacenterIdShift)
                | (workerId << SEQUENCE_LEN)
                | sequence;
    }

    /**
     * 阻塞到下一个毫秒，直到获得新的时间戳
     *
     * @param lastTimestamp 上次生成ID的时间截
     * @return 当前时间戳
     */
    private long tilNextMillis(long lastTimestamp) {
        long timestamp = timeGen();
        while (timestamp <= lastTimestamp) {
            timestamp = timeGen();
        }
        return timestamp;
    }

    /**
     * 高并发环境下，返回以毫秒为单位的当前时间
     *
     * @return 当前时间(毫秒)
     */
    private long timeGen() {
        return new Date().getTime();
    }

    /**
     * 获取工作机器ID
     * @return workerId
     */
    public long getWorkerId() {
        return workerId;
    }

    /**
     * 获取数据中心ID
     * @return datacenterId
     */
    public long getDatacenterId() {
        return datacenterId;
    }


}
