package org.befun.core.generator;

import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Snowflake多线程并发测试类
 * 
 * <AUTHOR> Assistant
 */
public class SnowflakeTest {
    
    /**
     * 多线程并发测试方法
     */
    public static void testConcurrency() {
        System.out.println("=== Snowflake 多线程并发测试 ===");
        
        // 测试参数
        final int THREAD_COUNT = 100;  // 线程数量
        final int IDS_PER_THREAD = 1000;  // 每个线程生成的ID数量
        final int TOTAL_IDS = THREAD_COUNT * IDS_PER_THREAD;
        
        // 创建Snowflake实例
        Snowflake snowflake = new Snowflake();
        System.out.println("WorkerId: " + snowflake.getWorkerId() + ", DatacenterId: " + snowflake.getDatacenterId());
        
        // 用于存储所有生成的ID
        ConcurrentHashMap<Long, Integer> idMap = new ConcurrentHashMap<>();
        List<Long> allIds = Collections.synchronizedList(new ArrayList<>());
        
        // 统计信息
        AtomicInteger duplicateCount = new AtomicInteger(0);
        AtomicInteger totalGenerated = new AtomicInteger(0);
        
        // 创建线程池
        ExecutorService executor = Executors.newFixedThreadPool(THREAD_COUNT);
        CountDownLatch latch = new CountDownLatch(THREAD_COUNT);
        
        long startTime = System.currentTimeMillis();
        
        // 提交任务
        for (int i = 0; i < THREAD_COUNT; i++) {
            final int threadId = i;
            executor.submit(() -> {
                try {
                    List<Long> threadIds = new ArrayList<>();
                    
                    for (int j = 0; j < IDS_PER_THREAD; j++) {
                        long id = snowflake.nextId();
                        threadIds.add(id);
                        allIds.add(id);
                        
                        // 检查重复
                        Integer previous = idMap.put(id, threadId);
                        if (previous != null) {
                            duplicateCount.incrementAndGet();
                            System.err.println("发现重复ID: " + id + " (线程" + threadId + " 和线程" + previous + ")");
                        }
                        
                        totalGenerated.incrementAndGet();
                    }
                    
                    // 验证单个线程内ID的递增性
                    for (int k = 1; k < threadIds.size(); k++) {
                        if (threadIds.get(k) <= threadIds.get(k-1)) {
                            System.err.println("线程" + threadId + "内ID非递增: " + 
                                threadIds.get(k-1) + " -> " + threadIds.get(k));
                        }
                    }
                    
                } catch (Exception e) {
                    System.err.println("线程" + threadId + "执行异常: " + e.getMessage());
                    e.printStackTrace();
                } finally {
                    latch.countDown();
                }
            });
        }
        
        try {
            // 等待所有线程完成
            latch.await();
            executor.shutdown();
            
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            
            // 输出测试结果
            System.out.println("\n=== 测试结果 ===");
            System.out.println("线程数量: " + THREAD_COUNT);
            System.out.println("每线程生成ID数: " + IDS_PER_THREAD);
            System.out.println("总计生成ID数: " + totalGenerated.get());
            System.out.println("预期生成ID数: " + TOTAL_IDS);
            System.out.println("唯一ID数量: " + idMap.size());
            System.out.println("重复ID数量: " + duplicateCount.get());
            System.out.println("执行时间: " + duration + "ms");
            System.out.println("平均TPS: " + (totalGenerated.get() * 1000L / duration) + " IDs/秒");
            
            // 验证ID唯一性
            if (duplicateCount.get() == 0) {
                System.out.println("✅ ID唯一性测试通过");
            } else {
                System.out.println("❌ ID唯一性测试失败，发现 " + duplicateCount.get() + " 个重复ID");
            }
            
            // 验证生成数量
            if (totalGenerated.get() == TOTAL_IDS && idMap.size() == TOTAL_IDS) {
                System.out.println("✅ ID生成数量测试通过");
            } else {
                System.out.println("❌ ID生成数量测试失败");
            }
            
            // 分析ID分布
            analyzeIdDistribution(allIds);
            
        } catch (InterruptedException e) {
            System.err.println("测试被中断: " + e.getMessage());
            Thread.currentThread().interrupt();
        }
    }
    
    /**
     * 分析ID分布情况
     */
    private static void analyzeIdDistribution(List<Long> ids) {
        if (ids.isEmpty()) return;
        
        System.out.println("\n=== ID分布分析 ===");
        
        // 排序ID列表
        List<Long> sortedIds = new ArrayList<>(ids);
        sortedIds.sort(Long::compareTo);
        
        long minId = sortedIds.get(0);
        long maxId = sortedIds.get(sortedIds.size() - 1);
        
        System.out.println("最小ID: " + minId);
        System.out.println("最大ID: " + maxId);
        System.out.println("ID范围: " + (maxId - minId));
        
        // 检查整体递增性（允许一定的乱序，因为多线程环境下时间戳可能相同）
        int outOfOrderCount = 0;
        for (int i = 1; i < sortedIds.size(); i++) {
            if (sortedIds.get(i) <= sortedIds.get(i-1)) {
                outOfOrderCount++;
            }
        }
        
        if (outOfOrderCount == 0) {
            System.out.println("✅ 全局ID递增性测试通过");
        } else {
            System.out.println("⚠️  发现 " + outOfOrderCount + " 个乱序ID（多线程环境下属正常现象）");
        }
        
        // 显示前10个和后10个ID作为样本
        System.out.println("\n前10个ID样本:");
        for (int i = 0; i < Math.min(10, sortedIds.size()); i++) {
            System.out.println("  " + sortedIds.get(i) + " (二进制: " + Long.toBinaryString(sortedIds.get(i)) + ")");
        }
        
        System.out.println("\n后10个ID样本:");
        int start = Math.max(0, sortedIds.size() - 10);
        for (int i = start; i < sortedIds.size(); i++) {
            System.out.println("  " + sortedIds.get(i) + " (二进制: " + Long.toBinaryString(sortedIds.get(i)) + ")");
        }
    }
    
    /**
     * 压力测试方法
     */
    public static void stressTest() {
        System.out.println("\n=== Snowflake 压力测试 ===");
        
        final int THREAD_COUNT = 200;  // 更多线程
        final int DURATION_SECONDS = 10;  // 测试持续时间
        
        Snowflake snowflake = new Snowflake();
        AtomicInteger totalGenerated = new AtomicInteger(0);
        AtomicInteger errorCount = new AtomicInteger(0);
        
        ExecutorService executor = Executors.newFixedThreadPool(THREAD_COUNT);
        boolean running = true;
        
        long startTime = System.currentTimeMillis();
        
        // 启动生成线程
        for (int i = 0; i < THREAD_COUNT; i++) {
            boolean finalRunning = running;
            executor.submit(() -> {
                while (finalRunning) {
                    try {
                        snowflake.nextId();
                        totalGenerated.incrementAndGet();
                    } catch (Exception e) {
                        errorCount.incrementAndGet();
                    }
                }
            });
        }
        
        // 运行指定时间
        try {
            Thread.sleep(DURATION_SECONDS * 1000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        running = false;
        executor.shutdown();
        
        try {
            executor.awaitTermination(5, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        long endTime = System.currentTimeMillis();
        long actualDuration = endTime - startTime;
        
        System.out.println("压力测试结果:");
        System.out.println("测试时间: " + actualDuration + "ms");
        System.out.println("生成ID总数: " + totalGenerated.get());
        System.out.println("错误次数: " + errorCount.get());
        System.out.println("平均TPS: " + (totalGenerated.get() * 1000L / actualDuration) + " IDs/秒");
        
        if (errorCount.get() == 0) {
            System.out.println("✅ 压力测试通过，无错误发生");
        } else {
            System.out.println("❌ 压力测试发现 " + errorCount.get() + " 个错误");
        }
    }

    public static void main(String[] args) {
        // 基本功能测试
        System.out.println("=== 基本功能测试 ===");
        Snowflake snowflake = new Snowflake();
        System.out.println("生成的ID示例: " + snowflake.nextId());
        
        // 多线程并发测试
        testConcurrency();
        
        // 压力测试
        //stressTest();

        System.out.println("\n=== 所有测试完成 ===");
    }
}
